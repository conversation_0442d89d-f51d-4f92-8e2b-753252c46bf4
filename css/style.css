/* ===================================
   LSTBook - Kids Educational Platform
   Main Stylesheet
   =================================== */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Comic+Neue:wght@300;400;700&family=Nunito:wght@300;400;600;700;800&display=swap');

/* ===================================
   CSS Variables - Color Palette
   =================================== */
:root {
  /* Main Brand Colors */
  --sky-blue: #4EC5F1;
  --sun-yellow: #FFCC00;
  --playful-red: #FF6B6B;
  --soft-green: #A5D6A7;

  /* Supporting Colors */
  --white: #FFFFFF;
  --light-gray: #F8F9FA;
  --medium-gray: #6C757D;
  --dark-gray: #343A40;

  /* Gradients */
  --hero-gradient: linear-gradient(135deg, var(--sky-blue) 0%, var(--soft-green) 100%);
  --button-gradient: linear-gradient(45deg, var(--sun-yellow) 0%, var(--playful-red) 100%);

  /* Typography */
  --font-primary: 'Comic Neue', cursive;
  --font-secondary: 'Nunito', sans-serif;

  /* Spacing */
  --container-max-width: 1200px;
  --section-padding: 4rem 0;
  --border-radius: 20px;
  --border-radius-small: 10px;

  /* Shadows */
  --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.2);
}

/* ===================================
   Reset and Base Styles
   =================================== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-secondary);
  font-weight: 400;
  line-height: 1.6;
  color: var(--dark-gray);
  background-color: #FFFDE7;
  overflow-x: hidden;
}

/* ===================================
   Typography
   =================================== */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-primary);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  color: var(--dark-gray);
}

h1 {
  font-size: 3rem;
  color: #4EC5F1;
  position: relative;
}

h1:not(.hero h1)::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, #4EC5F1 0%, #A5D6A7 100%);
  border-radius: 2px;
}

h2 {
  font-size: 2.5rem;
  color: #FF6B6B;
  position: relative;
}

h2:not(.newsletter h2)::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 50px;
  height: 3px;
  background: linear-gradient(90deg, #FF6B6B 0%, #FFCC00 100%);
  border-radius: 2px;
}

h3 {
  font-size: 2rem;
  color: #A5D6A7;
  position: relative;
}

h3::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 40px;
  height: 2px;
  background: #A5D6A7;
  border-radius: 1px;
}

h4 {
  font-size: 1.5rem;
}

p {
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

a {
  color: var(--sky-blue);
  text-decoration: none;
  transition: all 0.3s ease;
}

a:hover {
  color: var(--playful-red);
  text-decoration: underline;
}

/* ===================================
   Layout Components
   =================================== */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 1rem;
}

.section {
  padding: var(--section-padding);
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
}

.col {
  padding: 0 15px;
  flex: 1;
}

/* Grid System */
.grid {
  display: grid;
  gap: 2rem;
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* ===================================
   Buttons
   =================================== */
.btn {
  display: inline-block;
  padding: 1rem 2rem;
  font-family: var(--font-primary);
  font-size: 1.2rem;
  font-weight: 700;
  text-align: center;
  text-decoration: none;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-light);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.btn-primary {
  background: linear-gradient(45deg, #FFCC00 0%, #FF6B6B 100%);
  color: var(--white);
}

.btn-primary:hover {
  background: linear-gradient(45deg, #FFD700 0%, #FF5252 100%);
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
  color: var(--white);
  text-decoration: none;
}

.btn-secondary {
  background-color: #4EC5F1;
  color: var(--white);
}

.btn-secondary:hover {
  background-color: #A5D6A7;
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
  color: var(--white);
  text-decoration: none;
}

.btn-large {
  padding: 1.5rem 3rem;
  font-size: 1.4rem;
}

.btn-small {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

/* ===================================
   Cards
   =================================== */
.card {
  background: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  padding: 2.5rem;
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  height: 100%;
  border: 3px solid transparent;
  background-clip: padding-box;
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-rainbow);
  z-index: -1;
  margin: -3px;
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card:hover {
  transform: translateY(-8px) rotate(1deg);
  box-shadow: var(--shadow-magical);
}

.card:hover::before {
  opacity: 1;
}

.card-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.card-icon {
  font-size: 3rem;
  color: var(--sky-blue);
  margin-bottom: 1rem;
}

.card-title {
  font-size: 1.5rem;
  color: var(--playful-red);
  margin-bottom: 0.5rem;
}

.card-description {
  color: var(--medium-gray);
  font-size: 1rem;
  line-height: 1.5;
}

/* ===================================
   Header and Navigation
   =================================== */
.header {
  background: linear-gradient(90deg, #B8A9C9 0%, #E6D7A3 50%, #D4B5A0 100%) !important;
  box-shadow: var(--shadow-light);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  border-bottom: 3px solid #D4B5A0;
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
}

/* Logo Styling */
.logo {
  font-family: var(--font-primary);
  font-size: 2.5rem;
  font-weight: 700;
  text-decoration: none;
  color: #FFFFFF;
  transition: color 0.3s ease;
}

.logo:hover {
  color: #FFCC00;
  text-decoration: none;
}

/* Navigation Menu */
.nav-menu {
  display: flex;
  list-style: none;
  align-items: center;
  gap: 2rem;
}

.nav-item {
  position: relative;
}

.nav-link {
  font-family: var(--font-secondary);
  font-size: 1.1rem;
  font-weight: 600;
  color: #FFFFFF;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-small);
  transition: all 0.3s ease;
}

.nav-link:hover {
  color: #FFCC00;
  background-color: rgba(255, 255, 255, 0.1);
  text-decoration: none;
  border-bottom: 2px solid #FFCC00;
}

/* Mega Menu */
.mega-menu-item {
  position: relative;
}

.mega-menu {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #FFF5E6;
  box-shadow: var(--shadow-heavy);
  border-radius: var(--border-radius);
  border: 1px solid #E0E0E0;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
  width: 600px;
  max-width: 90vw;
}

.mega-menu-item:hover .mega-menu {
  opacity: 1;
  visibility: visible;
}

.mega-menu-content {
  padding: 2rem;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.mega-menu-section {
  text-align: left;
}

.mega-menu-title {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--light-gray);
}

.mega-menu-section:nth-child(1) .mega-menu-title {
  color: #4EC5F1;
}

.mega-menu-section:nth-child(2) .mega-menu-title {
  color: #FFCC00;
}

.mega-menu-section:nth-child(3) .mega-menu-title {
  color: #A5D6A7;
}

.mega-menu-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.mega-menu .mega-menu-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  color: var(--dark-gray);
  text-decoration: none;
  border-radius: var(--border-radius-small);
  transition: all 0.3s ease;
  font-size: 0.95rem;
}

.mega-menu .mega-menu-item:hover {
  background-color: #A5D6A7;
  color: var(--white);
  text-decoration: none;
  transform: translateX(5px);
}

.mega-menu .mega-menu-item i {
  font-size: 1.2rem;
  width: 20px;
  text-align: center;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 0.5rem;
}

.mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background-color: var(--dark-gray);
  margin: 3px 0;
  transition: 0.3s;
  border-radius: 2px;
}

/* ===================================
   Hero Section
   =================================== */
.hero {
  background: linear-gradient(180deg, #D4B5A0 0%, #E6D7A3 30%, #C8B5C8 70%, #B8A9C9 100%);
  color: var(--white);
  text-align: center;
  padding: 8rem 0 6rem;
  margin-top: 80px; /* Account for fixed header */
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    /* Sunset clouds */
    radial-gradient(ellipse 150px 75px at 20% 30%, rgba(255, 255, 255, 0.2) 40%, transparent 50%),
    radial-gradient(ellipse 120px 60px at 80% 25%, rgba(255, 255, 255, 0.15) 40%, transparent 50%),
    radial-gradient(ellipse 100px 50px at 15% 70%, rgba(255, 255, 255, 0.1) 40%, transparent 50%),
    radial-gradient(ellipse 130px 65px at 85% 75%, rgba(255, 255, 255, 0.12) 40%, transparent 50%),

    /* Soft pastel elements */
    radial-gradient(circle 25px at 25% 40%, rgba(230, 215, 163, 0.3) 40%, transparent 50%),
    radial-gradient(circle 20px at 75% 60%, rgba(200, 181, 200, 0.25) 40%, transparent 50%),
    radial-gradient(circle 15px at 35% 80%, rgba(212, 181, 160, 0.3) 40%, transparent 50%),
    radial-gradient(circle 18px at 65% 30%, rgba(184, 169, 201, 0.25) 40%, transparent 50%);

  animation: sunsetFloat 18s ease-in-out infinite;
}

@keyframes sunsetFloat {
  0%, 100% { transform: translateY(0px) scale(1); }
  33% { transform: translateY(-5px) scale(1.02); }
  66% { transform: translateY(-10px) scale(0.98); }
}

.hero h1 {
  font-size: 3.5rem;
  color: var(--white);
  margin-bottom: 1rem;
  font-family: var(--font-primary);
  position: relative;
  z-index: 2;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero p {
  font-size: 1.4rem;
  margin-bottom: 2rem;
  opacity: 0.95;
  position: relative;
  z-index: 2;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.hero .btn {
  position: relative;
  z-index: 2;
}

/* ===================================
   Game Categories Grid
   =================================== */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
}

.category-card {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid var(--light-gray);
}

/* Alternating category card colors */
.category-card:nth-child(4n+1) {
  border-left: 5px solid #FFCC00;
  background: linear-gradient(135deg, rgba(255, 204, 0, 0.05) 0%, var(--white) 100%);
}

.category-card:nth-child(4n+2) {
  border-left: 5px solid #4EC5F1;
  background: linear-gradient(135deg, rgba(78, 197, 241, 0.05) 0%, var(--white) 100%);
}

.category-card:nth-child(4n+3) {
  border-left: 5px solid #A5D6A7;
  background: linear-gradient(135deg, rgba(165, 214, 167, 0.05) 0%, var(--white) 100%);
}

.category-card:nth-child(4n+4) {
  border-left: 5px solid #FF6B6B;
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.05) 0%, var(--white) 100%);
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
  border-color: var(--sky-blue);
}

.category-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  display: block;
  color: var(--sky-blue);
}

.category-title {
  font-size: 1.8rem;
  background: var(--gradient-warm);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
  font-family: var(--font-secondary);
}

.category-description {
  color: var(--medium-gray);
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 1.5rem;
}

/* ===================================
   Featured Game Section
   =================================== */
.featured-game {
  background: var(--light-gray);
  border-radius: var(--border-radius);
  padding: 3rem;
  margin: 4rem 0;
  text-align: center;
}

.game-screenshot {
  width: 100%;
  max-width: 600px;
  height: 300px;
  background: var(--sky-blue);
  border-radius: var(--border-radius);
  margin: 2rem auto;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: var(--white);
  position: relative;
  overflow: hidden;
}

.game-screenshot::before {
  content: '🎮';
  font-size: 4rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* ===================================
   Video Section
   =================================== */
.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
}

.video-card {
  background: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  overflow: hidden;
  transition: all 0.3s ease;
}

.video-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.video-embed {
  width: 100%;
  height: 200px;
  border: none;
}

.video-info {
  padding: 1.5rem;
}

.video-title {
  font-size: 1.2rem;
  color: var(--playful-red);
  margin-bottom: 0.5rem;
}

.video-description {
  color: var(--medium-gray);
  font-size: 0.9rem;
  line-height: 1.4;
}

/* ===================================
   Newsletter Section
   =================================== */
.newsletter {
  background: var(--soft-green);
  color: var(--white);
  text-align: center;
  padding: 4rem 0;
  margin: 4rem 0;
  border-radius: var(--border-radius);
}

.newsletter h2 {
  color: var(--white);
  margin-bottom: 1rem;
}

.newsletter p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.newsletter-form {
  display: flex;
  gap: 1rem;
  max-width: 500px;
  margin: 0 auto;
}

.newsletter-form .form-control {
  flex: 1;
  border: none;
  padding: 1rem 1.5rem;
}

.newsletter-form .btn {
  white-space: nowrap;
}

/* ===================================
   Games Hub Styles
   =================================== */
.filters {
  background: var(--light-gray);
  padding: 2rem;
  border-radius: var(--border-radius);
  margin-bottom: 3rem;
}

.filter-group {
  margin-bottom: 1.5rem;
}

.filter-label {
  font-weight: 600;
  color: var(--dark-gray);
  margin-bottom: 0.5rem;
  display: block;
}

.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.filter-btn {
  padding: 0.5rem 1rem;
  background: var(--white);
  border: 2px solid var(--medium-gray);
  border-radius: var(--border-radius-small);
  color: var(--dark-gray);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.filter-btn:hover,
.filter-btn.active {
  background: var(--sky-blue);
  border-color: var(--sky-blue);
  color: var(--white);
}

.games-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.game-card {
  background: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  overflow: hidden;
  transition: all 0.3s ease;
}

.game-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.game-thumbnail {
  width: 100%;
  height: 150px;
  background: var(--sky-blue);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: var(--white);
}

.game-info {
  padding: 1.5rem;
}

.game-title {
  font-size: 1.3rem;
  color: var(--playful-red);
  margin-bottom: 0.5rem;
}

.game-description {
  color: var(--medium-gray);
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 1rem;
}

.game-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 0.8rem;
  color: var(--medium-gray);
}

.age-tag,
.difficulty-tag {
  background: var(--light-gray);
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-small);
  font-size: 0.7rem;
  font-weight: 600;
}

/* ===================================
   Footer
   =================================== */
.footer {
  background: #2C3E50;
  color: var(--white);
  text-align: center;
  padding: 3rem 0 2rem;
}

.footer-content {
  margin-bottom: 2rem;
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.social-link {
  display: inline-block;
  width: 50px;
  height: 50px;
  background: var(--sky-blue);
  color: var(--white);
  border-radius: 50%;
  text-align: center;
  line-height: 50px;
  font-size: 1.5rem;
  transition: all 0.3s ease;
}

.social-link:hover {
  background: #FFCC00;
  transform: translateY(-3px);
  color: #2C3E50;
  text-decoration: none;
}

.footer-text {
  color: var(--medium-gray);
  font-size: 0.9rem;
}

/* ===================================
   Forms
   =================================== */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--dark-gray);
}

.form-control {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--light-gray);
  border-radius: var(--border-radius-small);
  font-size: 1rem;
  font-family: var(--font-secondary);
  transition: all 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--sky-blue);
  box-shadow: 0 0 0 3px rgba(78, 197, 241, 0.1);
}

.form-control.error {
  border-color: var(--playful-red);
}

.error-message {
  color: var(--playful-red);
  font-size: 0.9rem;
  margin-top: 0.5rem;
  display: none;
}

.error-message.show {
  display: block;
}

/* ===================================
   Utility Classes
   =================================== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.hidden { display: none; }
.visible { display: block; }

/* ===================================
   Responsive Design
   =================================== */
@media (max-width: 768px) {
  /* Typography adjustments */
  h1 { font-size: 2.5rem; }
  h2 { font-size: 2rem; }
  h3 { font-size: 1.5rem; }

  .hero h1 { font-size: 3rem; }
  .hero p { font-size: 1.2rem; }

  /* Navigation */
  .nav-menu {
    position: fixed;
    top: 80px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 80px);
    background: var(--white);
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: 2rem;
    transition: left 0.3s ease;
    box-shadow: var(--shadow-medium);
  }

  .nav-menu.active {
    left: 0;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
  }

  .mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
  }

  .mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
  }

  /* Mega menu adjustments for mobile */
  .mega-menu {
    position: static;
    opacity: 1;
    visibility: visible;
    transform: none;
    box-shadow: none;
    background: #FFF5E6;
    margin-top: 0.5rem;
    width: 100%;
    max-width: none;
  }

  .mega-menu-content {
    padding: 1rem;
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .mega-menu .mega-menu-item:hover {
    transform: none;
  }

  /* Grid adjustments */
  .grid-2,
  .grid-3,
  .grid-4,
  .categories-grid,
  .video-grid,
  .games-grid {
    grid-template-columns: 1fr;
  }

  /* Button adjustments */
  .btn {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }

  .btn-large {
    padding: 1rem 2rem;
    font-size: 1.2rem;
  }

  /* Newsletter form */
  .newsletter-form {
    flex-direction: column;
  }

  /* Filter buttons */
  .filter-buttons {
    justify-content: center;
  }

  /* Spacing adjustments */
  .section {
    padding: 2rem 0;
  }

  .hero {
    padding: 6rem 0 4rem;
  }

  /* Container padding */
  .container {
    padding: 0 1rem;
  }

  /* Card adjustments */
  .card,
  .category-card,
  .game-card {
    margin-bottom: 1rem;
  }
}

@media (max-width: 480px) {
  .logo {
    font-size: 2rem;
  }

  .hero h1 {
    font-size: 2.5rem;
  }

  .hero p {
    font-size: 1.1rem;
  }

  .card,
  .category-card,
  .featured-game {
    padding: 1.5rem;
  }

  .btn {
    width: 100%;
    margin-bottom: 1rem;
  }

  .categories-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .category-icon {
    font-size: 3rem;
  }

  .game-screenshot {
    height: 200px;
  }

  .newsletter {
    padding: 2rem 1rem;
  }
}

/* ===================================
   Fun Animations & Keyframes
   =================================== */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes wiggle {
  0%, 7% {
    transform: rotateZ(0);
  }
  15% {
    transform: rotateZ(-15deg);
  }
  20% {
    transform: rotateZ(10deg);
  }
  25% {
    transform: rotateZ(-10deg);
  }
  30% {
    transform: rotateZ(6deg);
  }
  35% {
    transform: rotateZ(-4deg);
  }
  40%, 100% {
    transform: rotateZ(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes rainbow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Fun hover effects for interactive elements */
.category-card:hover {
  animation: wiggle 0.8s ease-in-out;
}

.game-card:hover {
  animation: pulse 0.6s ease-in-out;
}


