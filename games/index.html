<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Games Hub - LSTBook Educational Games for Kids</title>
    <meta name="description" content="Explore hundreds of educational games for kids ages 3-12. Filter by age, difficulty, and type to find the perfect learning games!">
    <meta name="keywords" content="kids games, educational games, learning games, children games, brain games, math games, alphabet games">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/favicon.ico">
    
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Main Stylesheet -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="/" class="logo">LSTBook</a>
                
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="/" class="nav-link">Home</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a href="/games/" class="nav-link">Games</a>
                        <div class="dropdown-menu">
                            <a href="/games/brain-boosters.html" class="dropdown-item">Brain Boosters</a>
                            <a href="/games/number-ninjas.html" class="dropdown-item">Number Ninjas</a>
                            <a href="/games/alphabet-explorers.html" class="dropdown-item">Alphabet Explorers</a>
                            <a href="/games/puzzle-planet.html" class="dropdown-item">Puzzle Planet</a>
                            <a href="/games/shape-shifters.html" class="dropdown-item">Shape Shifters</a>
                            <a href="/games/color-creativity.html" class="dropdown-item">Color & Creativity</a>
                            <a href="/games/sound-music.html" class="dropdown-item">Sound & Music</a>
                            <a href="/games/world-wonders.html" class="dropdown-item">World Wonders</a>
                            <a href="/games/daily-fun.html" class="dropdown-item">Daily Fun Games</a>
                            <a href="/games/create-code.html" class="dropdown-item">Create & Code</a>
                            <a href="/games/parent-picks.html" class="dropdown-item">Parent Picks</a>
                        </div>
                    </li>
                    <li class="nav-item">
                        <a href="/learn.html" class="nav-link">Learn</a>
                    </li>
                    <li class="nav-item">
                        <a href="/about.html" class="nav-link">About</a>
                    </li>
                    <li class="nav-item">
                        <a href="/contact.html" class="nav-link">Contact</a>
                    </li>
                    <li class="nav-item">
                        <a href="/app.html" class="nav-link">Coming Soon (App)</a>
                    </li>
                </ul>
                
                <div class="mobile-menu-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1>Games Hub</h1>
                <p>Discover amazing educational games designed for every age and skill level!</p>
            </div>
        </div>
    </section>

    <!-- Filters Section -->
    <section class="section">
        <div class="container">
            <div class="filters">
                <h3 class="text-center mb-3">Find the Perfect Games</h3>
                
                <!-- Age Filter -->
                <div class="filter-group">
                    <label class="filter-label">Age Group:</label>
                    <div class="filter-buttons">
                        <button class="filter-btn active" data-filter="age" data-value="all">All Ages</button>
                        <button class="filter-btn" data-filter="age" data-value="3-5">Ages 3-5</button>
                        <button class="filter-btn" data-filter="age" data-value="6-8">Ages 6-8</button>
                        <button class="filter-btn" data-filter="age" data-value="9-12">Ages 9-12</button>
                    </div>
                </div>
                
                <!-- Difficulty Filter -->
                <div class="filter-group">
                    <label class="filter-label">Difficulty Level:</label>
                    <div class="filter-buttons">
                        <button class="filter-btn active" data-filter="difficulty" data-value="all">All Levels</button>
                        <button class="filter-btn" data-filter="difficulty" data-value="easy">Easy</button>
                        <button class="filter-btn" data-filter="difficulty" data-value="medium">Medium</button>
                        <button class="filter-btn" data-filter="difficulty" data-value="hard">Hard</button>
                    </div>
                </div>
                
                <!-- Type Filter -->
                <div class="filter-group">
                    <label class="filter-label">Game Type:</label>
                    <div class="filter-buttons">
                        <button class="filter-btn active" data-filter="type" data-value="all">All Types</button>
                        <button class="filter-btn" data-filter="type" data-value="math">Math</button>
                        <button class="filter-btn" data-filter="type" data-value="logic">Logic</button>
                        <button class="filter-btn" data-filter="type" data-value="language">Language</button>
                        <button class="filter-btn" data-filter="type" data-value="creative">Creative</button>
                        <button class="filter-btn" data-filter="type" data-value="science">Science</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Games Grid -->
    <section class="section">
        <div class="container">
            <h2 class="text-center mb-4">All Games</h2>
            
            <div class="games-grid">
                <!-- Sample Games with different attributes for filtering -->
                
                <!-- Math Games -->
                <div class="game-card" data-age="3-5" data-difficulty="easy" data-type="math">
                    <div class="game-thumbnail">
                        <i class="fas fa-plus"></i>
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">Counting Fun</h3>
                        <p class="game-description">Learn to count from 1 to 10 with colorful objects and fun animations!</p>
                        <div class="game-meta">
                            <span class="age-tag">Ages 3-5</span>
                            <span class="difficulty-tag">Easy</span>
                        </div>
                        <a href="/games/sample-game/" class="btn btn-primary btn-small">Play Now</a>
                    </div>
                </div>

                <div class="game-card" data-age="6-8" data-difficulty="medium" data-type="math">
                    <div class="game-thumbnail">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">Addition Adventure</h3>
                        <p class="game-description">Master addition skills with exciting math challenges and puzzles!</p>
                        <div class="game-meta">
                            <span class="age-tag">Ages 6-8</span>
                            <span class="difficulty-tag">Medium</span>
                        </div>
                        <a href="/games/sample-game/" class="btn btn-primary btn-small">Play Now</a>
                    </div>
                </div>

                <div class="game-card" data-age="9-12" data-difficulty="hard" data-type="math">
                    <div class="game-thumbnail">
                        <i class="fas fa-square-root-alt"></i>
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">Multiplication Master</h3>
                        <p class="game-description">Become a multiplication expert with challenging math battles!</p>
                        <div class="game-meta">
                            <span class="age-tag">Ages 9-12</span>
                            <span class="difficulty-tag">Hard</span>
                        </div>
                        <a href="/games/sample-game/" class="btn btn-primary btn-small">Play Now</a>
                    </div>
                </div>

                <!-- Language Games -->
                <div class="game-card" data-age="3-5" data-difficulty="easy" data-type="language">
                    <div class="game-thumbnail">
                        <i class="fas fa-font"></i>
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">Letter Land</h3>
                        <p class="game-description">Explore the alphabet with fun characters and interactive activities!</p>
                        <div class="game-meta">
                            <span class="age-tag">Ages 3-5</span>
                            <span class="difficulty-tag">Easy</span>
                        </div>
                        <a href="/games/sample-game/" class="btn btn-primary btn-small">Play Now</a>
                    </div>
                </div>

                <div class="game-card" data-age="6-8" data-difficulty="medium" data-type="language">
                    <div class="game-thumbnail">
                        <i class="fas fa-book-open"></i>
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">Word Builder</h3>
                        <p class="game-description">Build words and improve spelling with this engaging word game!</p>
                        <div class="game-meta">
                            <span class="age-tag">Ages 6-8</span>
                            <span class="difficulty-tag">Medium</span>
                        </div>
                        <a href="/games/sample-game/" class="btn btn-primary btn-small">Play Now</a>
                    </div>
                </div>

                <!-- Logic Games -->
                <div class="game-card" data-age="6-8" data-difficulty="medium" data-type="logic">
                    <div class="game-thumbnail">
                        <i class="fas fa-puzzle-piece"></i>
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">Pattern Detective</h3>
                        <p class="game-description">Solve pattern puzzles and develop logical thinking skills!</p>
                        <div class="game-meta">
                            <span class="age-tag">Ages 6-8</span>
                            <span class="difficulty-tag">Medium</span>
                        </div>
                        <a href="/games/sample-game/" class="btn btn-primary btn-small">Play Now</a>
                    </div>
                </div>

                <div class="game-card" data-age="9-12" data-difficulty="hard" data-type="logic">
                    <div class="game-thumbnail">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">Logic Master</h3>
                        <p class="game-description">Challenge your mind with complex logic puzzles and brain teasers!</p>
                        <div class="game-meta">
                            <span class="age-tag">Ages 9-12</span>
                            <span class="difficulty-tag">Hard</span>
                        </div>
                        <a href="/games/sample-game/" class="btn btn-primary btn-small">Play Now</a>
                    </div>
                </div>

                <!-- Creative Games -->
                <div class="game-card" data-age="3-5" data-difficulty="easy" data-type="creative">
                    <div class="game-thumbnail">
                        <i class="fas fa-palette"></i>
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">Color Magic</h3>
                        <p class="game-description">Create beautiful artwork with magical colors and brushes!</p>
                        <div class="game-meta">
                            <span class="age-tag">Ages 3-5</span>
                            <span class="difficulty-tag">Easy</span>
                        </div>
                        <a href="/games/sample-game/" class="btn btn-primary btn-small">Play Now</a>
                    </div>
                </div>

                <div class="game-card" data-age="6-8" data-difficulty="medium" data-type="creative">
                    <div class="game-thumbnail">
                        <i class="fas fa-music"></i>
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">Music Maker</h3>
                        <p class="game-description">Compose your own melodies and learn about musical instruments!</p>
                        <div class="game-meta">
                            <span class="age-tag">Ages 6-8</span>
                            <span class="difficulty-tag">Medium</span>
                        </div>
                        <a href="/games/sample-game/" class="btn btn-primary btn-small">Play Now</a>
                    </div>
                </div>

                <!-- Science Games -->
                <div class="game-card" data-age="9-12" data-difficulty="medium" data-type="science">
                    <div class="game-thumbnail">
                        <i class="fas fa-flask"></i>
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">Science Lab</h3>
                        <p class="game-description">Conduct virtual experiments and discover the wonders of science!</p>
                        <div class="game-meta">
                            <span class="age-tag">Ages 9-12</span>
                            <span class="difficulty-tag">Medium</span>
                        </div>
                        <a href="/games/sample-game/" class="btn btn-primary btn-small">Play Now</a>
                    </div>
                </div>

                <div class="game-card" data-age="6-8" data-difficulty="easy" data-type="science">
                    <div class="game-thumbnail">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">Planet Explorer</h3>
                        <p class="game-description">Explore the solar system and learn about planets and space!</p>
                        <div class="game-meta">
                            <span class="age-tag">Ages 6-8</span>
                            <span class="difficulty-tag">Easy</span>
                        </div>
                        <a href="/games/sample-game/" class="btn btn-primary btn-small">Play Now</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="social-links">
                    <a href="#" class="social-link" aria-label="Facebook">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="social-link" aria-label="Twitter">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="social-link" aria-label="Instagram">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="#" class="social-link" aria-label="YouTube">
                        <i class="fab fa-youtube"></i>
                    </a>
                </div>
                
                <div class="footer-links">
                    <a href="/privacy.html">Privacy Policy</a> | 
                    <a href="/terms.html">Terms of Use</a> | 
                    <a href="/contact.html">Contact Us</a>
                </div>
            </div>
            
            <div class="footer-text">
                <p>&copy; 2024 LSTBook.com - Making Learning Fun for Kids Everywhere! 🎉</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="../js/scripts.js"></script>
</body>
</html>
