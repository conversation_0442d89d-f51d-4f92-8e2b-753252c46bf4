<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Math Adventure Island - Sample Game | LSTBook</title>
    <meta name="description" content="Play Math Adventure Island, a fun educational game where kids learn math while exploring a magical island!">
    <meta name="keywords" content="math game, kids game, educational game, adventure game, learning game">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/favicon.ico">
    
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Main Stylesheet -->
    <link rel="stylesheet" href="../../css/style.css">
    
    <style>
        .game-container {
            background: var(--light-gray);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin: 2rem 0;
            text-align: center;
        }
        
        .game-iframe-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            height: 600px;
            margin: 0 auto;
            background: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-medium);
            overflow: hidden;
        }
        
        .game-iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: var(--border-radius);
        }
        
        .loading-spinner {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 2rem;
            color: var(--sky-blue);
        }
        
        .game-info {
            background: var(--white);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin: 2rem 0;
            box-shadow: var(--shadow-light);
        }
        
        .game-controls {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin: 2rem 0;
            flex-wrap: wrap;
        }
        
        @media (max-width: 768px) {
            .game-iframe-container {
                height: 400px;
            }
            
            .game-controls {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="/" class="logo">LSTBook</a>
                
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="/" class="nav-link">Home</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a href="/games/" class="nav-link">Games</a>
                        <div class="dropdown-menu">
                            <a href="/games/brain-boosters.html" class="dropdown-item">Brain Boosters</a>
                            <a href="/games/number-ninjas.html" class="dropdown-item">Number Ninjas</a>
                            <a href="/games/alphabet-explorers.html" class="dropdown-item">Alphabet Explorers</a>
                            <a href="/games/puzzle-planet.html" class="dropdown-item">Puzzle Planet</a>
                            <a href="/games/shape-shifters.html" class="dropdown-item">Shape Shifters</a>
                            <a href="/games/color-creativity.html" class="dropdown-item">Color & Creativity</a>
                            <a href="/games/sound-music.html" class="dropdown-item">Sound & Music</a>
                            <a href="/games/world-wonders.html" class="dropdown-item">World Wonders</a>
                            <a href="/games/daily-fun.html" class="dropdown-item">Daily Fun Games</a>
                            <a href="/games/create-code.html" class="dropdown-item">Create & Code</a>
                            <a href="/games/parent-picks.html" class="dropdown-item">Parent Picks</a>
                        </div>
                    </li>
                    <li class="nav-item">
                        <a href="/learn.html" class="nav-link">Learn</a>
                    </li>
                    <li class="nav-item">
                        <a href="/about.html" class="nav-link">About</a>
                    </li>
                    <li class="nav-item">
                        <a href="/contact.html" class="nav-link">Contact</a>
                    </li>
                    <li class="nav-item">
                        <a href="/app.html" class="nav-link">Coming Soon (App)</a>
                    </li>
                </ul>
                
                <div class="mobile-menu-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </nav>
        </div>
    </header>

    <!-- Game Section -->
    <section class="section" style="margin-top: 80px;">
        <div class="container">
            <!-- Game Header -->
            <div class="text-center mb-4">
                <h1>Math Adventure Island</h1>
                <p>Join Captain Numbers on an exciting adventure to solve math puzzles and save the island!</p>
                
                <div class="game-meta" style="display: flex; justify-content: center; gap: 1rem; margin: 1rem 0;">
                    <span class="age-tag" style="background: var(--sky-blue); color: white; padding: 0.5rem 1rem; border-radius: 20px;">Ages 6-8</span>
                    <span class="difficulty-tag" style="background: var(--soft-green); color: white; padding: 0.5rem 1rem; border-radius: 20px;">Medium</span>
                    <span class="type-tag" style="background: var(--sun-yellow); color: white; padding: 0.5rem 1rem; border-radius: 20px;">Math</span>
                </div>
            </div>

            <!-- Game Container -->
            <div class="game-container">
                <div class="game-iframe-container">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                    <iframe class="game-iframe" src="game.html" title="Math Adventure Island Game"></iframe>
                </div>
                
                <div class="game-controls">
                    <button class="btn btn-secondary" onclick="restartGame()">
                        <i class="fas fa-redo"></i> Restart Game
                    </button>
                    <button class="btn btn-primary" onclick="toggleFullscreen()">
                        <i class="fas fa-expand"></i> Fullscreen
                    </button>
                    <a href="/games/" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Games
                    </a>
                </div>
            </div>

            <!-- Game Information -->
            <div class="game-info">
                <div class="row">
                    <div class="col">
                        <h3><i class="fas fa-info-circle" style="color: var(--sky-blue);"></i> About This Game</h3>
                        <p>Math Adventure Island is an exciting educational game that combines adventure and learning. Players help Captain Numbers navigate through different islands, solving math problems to unlock new areas and collect treasures!</p>
                        
                        <h4><i class="fas fa-target" style="color: var(--playful-red);"></i> Learning Objectives</h4>
                        <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                            <li>Practice addition and subtraction skills</li>
                            <li>Develop problem-solving strategies</li>
                            <li>Improve number recognition and counting</li>
                            <li>Build confidence in math abilities</li>
                            <li>Learn through engaging storytelling</li>
                        </ul>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col">
                        <h4><i class="fas fa-gamepad" style="color: var(--soft-green);"></i> How to Play</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1rem;">
                            <div style="text-align: center;">
                                <i class="fas fa-mouse-pointer" style="font-size: 2rem; color: var(--sky-blue); margin-bottom: 0.5rem;"></i>
                                <p><strong>Click</strong> to interact with objects and characters</p>
                            </div>
                            <div style="text-align: center;">
                                <i class="fas fa-calculator" style="font-size: 2rem; color: var(--sun-yellow); margin-bottom: 0.5rem;"></i>
                                <p><strong>Solve</strong> math problems to progress</p>
                            </div>
                            <div style="text-align: center;">
                                <i class="fas fa-map" style="font-size: 2rem; color: var(--playful-red); margin-bottom: 0.5rem;"></i>
                                <p><strong>Explore</strong> different islands and areas</p>
                            </div>
                            <div style="text-align: center;">
                                <i class="fas fa-trophy" style="font-size: 2rem; color: var(--soft-green); margin-bottom: 0.5rem;"></i>
                                <p><strong>Collect</strong> treasures and achievements</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Games -->
            <div class="section">
                <h3 class="text-center mb-4">More Math Games You'll Love</h3>
                <div class="grid-3">
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-plus card-icon"></i>
                            <h4 class="card-title">Counting Fun</h4>
                        </div>
                        <p class="card-description">Learn to count from 1 to 10 with colorful objects and fun animations!</p>
                        <a href="/games/sample-game/" class="btn btn-primary btn-small">Play Now</a>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-minus card-icon"></i>
                            <h4 class="card-title">Subtraction Safari</h4>
                        </div>
                        <p class="card-description">Go on a safari adventure while learning subtraction skills!</p>
                        <a href="/games/sample-game/" class="btn btn-primary btn-small">Play Now</a>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-times card-icon"></i>
                            <h4 class="card-title">Multiplication Master</h4>
                        </div>
                        <p class="card-description">Become a multiplication expert with challenging math battles!</p>
                        <a href="/games/sample-game/" class="btn btn-primary btn-small">Play Now</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="social-links">
                    <a href="#" class="social-link" aria-label="Facebook">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="social-link" aria-label="Twitter">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="social-link" aria-label="Instagram">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="#" class="social-link" aria-label="YouTube">
                        <i class="fab fa-youtube"></i>
                    </a>
                </div>
                
                <div class="footer-links">
                    <a href="/privacy.html">Privacy Policy</a> | 
                    <a href="/terms.html">Terms of Use</a> | 
                    <a href="/contact.html">Contact Us</a>
                </div>
            </div>
            
            <div class="footer-text">
                <p>&copy; 2024 LSTBook.com - Making Learning Fun for Kids Everywhere! 🎉</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="../../js/scripts.js"></script>
    <script>
        function restartGame() {
            const iframe = document.querySelector('.game-iframe');
            iframe.src = iframe.src;
        }
        
        function toggleFullscreen() {
            const container = document.querySelector('.game-iframe-container');
            if (!document.fullscreenElement) {
                container.requestFullscreen().catch(err => {
                    console.log('Error attempting to enable fullscreen:', err);
                });
            } else {
                document.exitFullscreen();
            }
        }
    </script>
</body>
</html>
