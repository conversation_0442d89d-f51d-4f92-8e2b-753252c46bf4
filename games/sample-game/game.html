<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Math Adventure Island - Game</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Comic Neue', cursive;
            background: linear-gradient(135deg, #4EC5F1 0%, #A5D6A7 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            overflow: hidden;
        }
        
        .game-screen {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            color: white;
            position: relative;
        }
        
        .game-title {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: bounce 2s infinite;
        }
        
        .game-subtitle {
            font-size: 1.5rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .coming-soon {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            margin: 2rem;
            box-shadow: 0 8px 30px rgba(0,0,0,0.2);
            max-width: 600px;
        }
        
        .game-icon {
            font-size: 5rem;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1rem;
            transition: transform 0.3s ease;
        }
        
        .feature:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .cta-button {
            background: linear-gradient(45deg, #FFCC00, #FF6B6B);
            color: white;
            border: none;
            padding: 1rem 2rem;
            font-size: 1.2rem;
            font-weight: bold;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin: 1rem;
            text-decoration: none;
            display: inline-block;
        }
        
        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }
        
        .floating-element {
            position: absolute;
            font-size: 2rem;
            opacity: 0.3;
            animation: float 6s ease-in-out infinite;
        }
        
        .floating-element:nth-child(1) {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .floating-element:nth-child(2) {
            top: 20%;
            right: 15%;
            animation-delay: 1s;
        }
        
        .floating-element:nth-child(3) {
            bottom: 20%;
            left: 20%;
            animation-delay: 2s;
        }
        
        .floating-element:nth-child(4) {
            bottom: 30%;
            right: 10%;
            animation-delay: 3s;
        }
        
        .floating-element:nth-child(5) {
            top: 50%;
            left: 5%;
            animation-delay: 4s;
        }
        
        .floating-element:nth-child(6) {
            top: 60%;
            right: 5%;
            animation-delay: 5s;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-20px);
            }
            60% {
                transform: translateY(-10px);
            }
        }
        
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            33% {
                transform: translateY(-20px) rotate(120deg);
            }
            66% {
                transform: translateY(10px) rotate(240deg);
            }
        }
        
        @media (max-width: 768px) {
            .game-title {
                font-size: 2rem;
            }
            
            .game-subtitle {
                font-size: 1.2rem;
            }
            
            .coming-soon {
                padding: 2rem;
                margin: 1rem;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="game-screen">
        <!-- Floating Background Elements -->
        <div class="floating-elements">
            <div class="floating-element">🎮</div>
            <div class="floating-element">🧮</div>
            <div class="floating-element">🏝️</div>
            <div class="floating-element">⭐</div>
            <div class="floating-element">🎯</div>
            <div class="floating-element">🏆</div>
        </div>
        
        <div class="coming-soon">
            <div class="game-icon">🎮</div>
            <h1 class="game-title">Math Adventure Island</h1>
            <p class="game-subtitle">An Amazing Educational Game Coming Soon!</p>
            
            <p style="font-size: 1.1rem; margin-bottom: 2rem;">
                Join Captain Numbers on an epic adventure where math meets fun! 
                Solve puzzles, explore magical islands, and become a math hero!
            </p>
            
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">🧠</div>
                    <h4>Brain Training</h4>
                    <p>Develop critical thinking skills</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">🎯</div>
                    <h4>Fun Challenges</h4>
                    <p>Engaging math puzzles</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">🏆</div>
                    <h4>Achievements</h4>
                    <p>Unlock rewards and badges</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">👥</div>
                    <h4>Multiplayer</h4>
                    <p>Play with friends</p>
                </div>
            </div>
            
            <div style="margin-top: 2rem;">
                <p style="font-size: 1.2rem; margin-bottom: 1rem;">
                    <strong>🚀 This game is currently in development!</strong>
                </p>
                <p style="margin-bottom: 2rem;">
                    We're working hard to bring you the most amazing educational gaming experience. 
                    Stay tuned for updates!
                </p>
                
                <a href="/games/" class="cta-button">
                    🎮 Explore More Games
                </a>
                <a href="/app.html" class="cta-button">
                    📱 Get Notified
                </a>
            </div>
            
            <div style="margin-top: 2rem; font-size: 0.9rem; opacity: 0.8;">
                <p>Expected Features:</p>
                <ul style="list-style: none; padding: 0;">
                    <li>✅ Interactive Math Problems</li>
                    <li>✅ Beautiful Island Environments</li>
                    <li>✅ Character Progression System</li>
                    <li>✅ Adaptive Difficulty Levels</li>
                    <li>✅ Parent Progress Reports</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        // Add some interactive elements
        document.addEventListener('DOMContentLoaded', function() {
            // Add click effects to features
            const features = document.querySelectorAll('.feature');
            features.forEach(feature => {
                feature.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'translateY(-5px)';
                    }, 150);
                });
            });
            
            // Add floating animation to game icon
            const gameIcon = document.querySelector('.game-icon');
            let rotation = 0;
            setInterval(() => {
                rotation += 1;
                gameIcon.style.transform = `rotate(${rotation}deg)`;
            }, 50);
            
            // Add random sparkle effects
            function createSparkle() {
                const sparkle = document.createElement('div');
                sparkle.innerHTML = '✨';
                sparkle.style.position = 'absolute';
                sparkle.style.fontSize = '1rem';
                sparkle.style.pointerEvents = 'none';
                sparkle.style.left = Math.random() * 100 + '%';
                sparkle.style.top = Math.random() * 100 + '%';
                sparkle.style.animation = 'float 3s ease-out forwards';
                sparkle.style.opacity = '0.7';
                
                document.querySelector('.floating-elements').appendChild(sparkle);
                
                setTimeout(() => {
                    sparkle.remove();
                }, 3000);
            }
            
            // Create sparkles periodically
            setInterval(createSparkle, 2000);
        });
    </script>
</body>
</html>
