<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brain Boosters - Educational Brain Games for Kids | LSTBook</title>
    <meta name="description" content="Challenge your mind with fun brain booster games! Memory games, puzzles, and critical thinking activities for kids ages 3-12.">
    <meta name="keywords" content="brain games, memory games, puzzles, critical thinking, kids brain training, educational games">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/favicon.ico">
    
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Main Stylesheet -->
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="/" class="logo">LSTBook</a>
                
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="/" class="nav-link">Home</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a href="/games/" class="nav-link">Games</a>
                        <div class="dropdown-menu">
                            <a href="/games/brain-boosters.html" class="dropdown-item">Brain Boosters</a>
                            <a href="/games/number-ninjas.html" class="dropdown-item">Number Ninjas</a>
                            <a href="/games/alphabet-explorers.html" class="dropdown-item">Alphabet Explorers</a>
                            <a href="/games/puzzle-planet.html" class="dropdown-item">Puzzle Planet</a>
                            <a href="/games/shape-shifters.html" class="dropdown-item">Shape Shifters</a>
                            <a href="/games/color-creativity.html" class="dropdown-item">Color & Creativity</a>
                            <a href="/games/sound-music.html" class="dropdown-item">Sound & Music</a>
                            <a href="/games/world-wonders.html" class="dropdown-item">World Wonders</a>
                            <a href="/games/daily-fun.html" class="dropdown-item">Daily Fun Games</a>
                            <a href="/games/create-code.html" class="dropdown-item">Create & Code</a>
                            <a href="/games/parent-picks.html" class="dropdown-item">Parent Picks</a>
                        </div>
                    </li>
                    <li class="nav-item">
                        <a href="/learn.html" class="nav-link">Learn</a>
                    </li>
                    <li class="nav-item">
                        <a href="/about.html" class="nav-link">About</a>
                    </li>
                    <li class="nav-item">
                        <a href="/contact.html" class="nav-link">Contact</a>
                    </li>
                    <li class="nav-item">
                        <a href="/app.html" class="nav-link">Coming Soon (App)</a>
                    </li>
                </ul>
                
                <div class="mobile-menu-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1><i class="fas fa-brain"></i> Brain Boosters</h1>
                <p>Challenge your mind with puzzles, memory games, and critical thinking activities that make learning fun!</p>
                <a href="#games" class="btn btn-primary btn-large">Start Brain Training!</a>
            </div>
        </div>
    </section>

    <!-- Category Info Section -->
    <section class="section">
        <div class="container">
            <div class="row">
                <div class="col">
                    <h2 class="text-center mb-4">Why Brain Boosters?</h2>
                    <div class="grid-3">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-lightbulb card-icon"></i>
                                <h3 class="card-title">Improve Memory</h3>
                            </div>
                            <p class="card-description">Strengthen memory skills through fun and engaging memory games and challenges.</p>
                        </div>
                        
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-puzzle-piece card-icon"></i>
                                <h3 class="card-title">Problem Solving</h3>
                            </div>
                            <p class="card-description">Develop critical thinking and problem-solving abilities with interactive puzzles.</p>
                        </div>
                        
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-rocket card-icon"></i>
                                <h3 class="card-title">Boost Focus</h3>
                            </div>
                            <p class="card-description">Enhance concentration and attention span through carefully designed brain exercises.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Games Section -->
    <section class="section" id="games" style="background: var(--light-gray);">
        <div class="container">
            <h2 class="text-center mb-4">Brain Booster Games</h2>
            
            <div class="games-grid">
                <!-- Memory Master -->
                <div class="game-card" data-age="6-8" data-difficulty="medium" data-type="logic">
                    <div class="game-thumbnail">
                        <i class="fas fa-memory" style="color: var(--sky-blue);"></i>
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">Memory Master</h3>
                        <p class="game-description">Test and improve your memory with this classic memory matching game featuring colorful cards and fun themes!</p>
                        <div class="game-meta">
                            <span class="age-tag">Ages 6-8</span>
                            <span class="difficulty-tag">Medium</span>
                        </div>
                        <a href="/games/sample-game/" class="btn btn-primary btn-small">Play Now</a>
                    </div>
                </div>

                <!-- Pattern Detective -->
                <div class="game-card" data-age="9-12" data-difficulty="hard" data-type="logic">
                    <div class="game-thumbnail">
                        <i class="fas fa-search" style="color: var(--playful-red);"></i>
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">Pattern Detective</h3>
                        <p class="game-description">Become a pattern detective! Solve complex pattern puzzles and develop logical thinking skills.</p>
                        <div class="game-meta">
                            <span class="age-tag">Ages 9-12</span>
                            <span class="difficulty-tag">Hard</span>
                        </div>
                        <a href="/games/sample-game/" class="btn btn-primary btn-small">Play Now</a>
                    </div>
                </div>

                <!-- Logic Puzzles -->
                <div class="game-card" data-age="6-8" data-difficulty="medium" data-type="logic">
                    <div class="game-thumbnail">
                        <i class="fas fa-brain" style="color: var(--soft-green);"></i>
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">Logic Puzzles</h3>
                        <p class="game-description">Challenge your logical thinking with a variety of brain-teasing puzzles and riddles!</p>
                        <div class="game-meta">
                            <span class="age-tag">Ages 6-8</span>
                            <span class="difficulty-tag">Medium</span>
                        </div>
                        <a href="/games/sample-game/" class="btn btn-primary btn-small">Play Now</a>
                    </div>
                </div>

                <!-- Sequence Builder -->
                <div class="game-card" data-age="3-5" data-difficulty="easy" data-type="logic">
                    <div class="game-thumbnail">
                        <i class="fas fa-list-ol" style="color: var(--sun-yellow);"></i>
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">Sequence Builder</h3>
                        <p class="game-description">Learn about sequences and patterns by completing fun and colorful sequence puzzles!</p>
                        <div class="game-meta">
                            <span class="age-tag">Ages 3-5</span>
                            <span class="difficulty-tag">Easy</span>
                        </div>
                        <a href="/games/sample-game/" class="btn btn-primary btn-small">Play Now</a>
                    </div>
                </div>

                <!-- Mind Maze -->
                <div class="game-card" data-age="9-12" data-difficulty="hard" data-type="logic">
                    <div class="game-thumbnail">
                        <i class="fas fa-route" style="color: var(--playful-red);"></i>
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">Mind Maze</h3>
                        <p class="game-description">Navigate through challenging mazes while solving brain puzzles along the way!</p>
                        <div class="game-meta">
                            <span class="age-tag">Ages 9-12</span>
                            <span class="difficulty-tag">Hard</span>
                        </div>
                        <a href="/games/sample-game/" class="btn btn-primary btn-small">Play Now</a>
                    </div>
                </div>

                <!-- Quick Thinking -->
                <div class="game-card" data-age="6-8" data-difficulty="medium" data-type="logic">
                    <div class="game-thumbnail">
                        <i class="fas fa-stopwatch" style="color: var(--sky-blue);"></i>
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">Quick Thinking</h3>
                        <p class="game-description">Test your quick thinking skills with fast-paced brain challenges and time-based puzzles!</p>
                        <div class="game-meta">
                            <span class="age-tag">Ages 6-8</span>
                            <span class="difficulty-tag">Medium</span>
                        </div>
                        <a href="/games/sample-game/" class="btn btn-primary btn-small">Play Now</a>
                    </div>
                </div>

                <!-- Color Memory -->
                <div class="game-card" data-age="3-5" data-difficulty="easy" data-type="logic">
                    <div class="game-thumbnail">
                        <i class="fas fa-palette" style="color: var(--soft-green);"></i>
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">Color Memory</h3>
                        <p class="game-description">Improve memory skills by remembering and matching beautiful color sequences!</p>
                        <div class="game-meta">
                            <span class="age-tag">Ages 3-5</span>
                            <span class="difficulty-tag">Easy</span>
                        </div>
                        <a href="/games/sample-game/" class="btn btn-primary btn-small">Play Now</a>
                    </div>
                </div>

                <!-- Brain Trainer -->
                <div class="game-card" data-age="9-12" data-difficulty="hard" data-type="logic">
                    <div class="game-thumbnail">
                        <i class="fas fa-dumbbell" style="color: var(--sun-yellow);"></i>
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">Brain Trainer</h3>
                        <p class="game-description">Complete daily brain training exercises to keep your mind sharp and focused!</p>
                        <div class="game-meta">
                            <span class="age-tag">Ages 9-12</span>
                            <span class="difficulty-tag">Hard</span>
                        </div>
                        <a href="/games/sample-game/" class="btn btn-primary btn-small">Play Now</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Categories -->
    <section class="section">
        <div class="container">
            <h2 class="text-center mb-4">Explore More Categories</h2>
            <div class="grid-3">
                <div class="category-card" data-category="/games/puzzle-planet.html">
                    <i class="fas fa-puzzle-piece category-icon" style="color: var(--soft-green);"></i>
                    <h3 class="category-title">Puzzle Planet</h3>
                    <p class="category-description">Solve amazing puzzles, jigsaws, and brain teasers!</p>
                    <a href="/games/puzzle-planet.html" class="btn btn-secondary">Explore Games</a>
                </div>

                <div class="category-card" data-category="/games/number-ninjas.html">
                    <i class="fas fa-calculator category-icon" style="color: var(--sun-yellow);"></i>
                    <h3 class="category-title">Number Ninjas</h3>
                    <p class="category-description">Master math skills with exciting number games!</p>
                    <a href="/games/number-ninjas.html" class="btn btn-secondary">Explore Games</a>
                </div>

                <div class="category-card" data-category="/games/create-code.html">
                    <i class="fas fa-code category-icon" style="color: var(--playful-red);"></i>
                    <h3 class="category-title">Create & Code</h3>
                    <p class="category-description">Learn basic programming through fun coding games!</p>
                    <a href="/games/create-code.html" class="btn btn-secondary">Explore Games</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="social-links">
                    <a href="#" class="social-link" aria-label="Facebook">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="social-link" aria-label="Twitter">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="social-link" aria-label="Instagram">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="#" class="social-link" aria-label="YouTube">
                        <i class="fab fa-youtube"></i>
                    </a>
                </div>
                
                <div class="footer-links">
                    <a href="/privacy.html">Privacy Policy</a> | 
                    <a href="/terms.html">Terms of Use</a> | 
                    <a href="/contact.html">Contact Us</a>
                </div>
            </div>
            
            <div class="footer-text">
                <p>&copy; 2024 LSTBook.com - Making Learning Fun for Kids Everywhere! 🎉</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="../js/scripts.js"></script>
</body>
</html>
